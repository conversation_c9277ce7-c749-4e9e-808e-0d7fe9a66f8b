# CW Water Global Budget Planning 2025 💧

A comprehensive Streamlit application for CW Water's global budget planning, addressing multi-dimensional data analysis, income statement projections, and expense modeling.

## Overview

CW Water is an international corporation selling products in 84 countries with 60% of sales from North America. This application helps plan the global budget for 2025 by analyzing sales projections from three divisions: US-East, US-West, and International.

## Features

### Challenge 1: Multi-Dimensional Analysis

- **TM1-style Data Model**: Clean, multi-dimensional cube structure
- **Dynamic Filtering**: Filter by Division, Customer, Channel, Region, SKU with "All" options
- **Time Aggregations**: MTD, QTD, YTD calculations
- **Interactive Visualizations**:
  - Monthly volume distribution charts
  - Division and channel pie charts
  - Quarterly analysis with YTD trends
- **Flexible Data Views**: Multiple aggregation levels (Customer, Channel, Region, SKU, Division)

### Challenge 2: Income Statement Template

- **Volume Summary**: Monthly and annual volume by region and channel
- **Seasonality Analysis**:
  - Overall monthly seasonality percentages
  - SKU-specific seasonality patterns
- **Revenue Calculations**:
  - Integration with pricing data from different channels
  - Monthly revenue trends
  - Average revenue per case calculations
- **Key Metrics**: Total revenue, volume, and performance indicators

### Challenge 3: Expenses Model

- **Rebate Calculations**:
  - 7-Eleven: 10 cents/bottle for 500mL bottles (24 bottles per case)
  - Safeway: 95 cents/case for all cases sold
- **Delivery Van Cost Model**:
  - Fleet of 1,000 vans averaging 10,234 miles/month
  - Dynamic gasoline cost calculator
  - Monthly and annual cost projections
- **Expense Placeholders**: 10 categories of business expenses
- **Projected Income Statement**: Complete financial summary

## Data Structure

The application processes data from four Excel sheets:

- **US - East**: 1,580 records with Customer, Channel, State, SKU, and monthly volumes
- **US - West**: 1,869 records with similar structure
- **INTL**: 3,074 records with Country instead of State
- **Prices**: 6 SKUs with pricing by channel (Convenience, Drug, Food, Mass, Other)

## SKU Information

- 330mL 6-pack
- 500mL loose (0.5L LS)
- 500mL 6-pack (0.5L 6PK)
- 1.0L loose (1.0L LS)
- 1.0L 6-pack (1.0L 6PK)
- 1.5L loose

## Installation & Usage

### Prerequisites

- Python environment with conda
- Existing 'cubewise' conda environment

### Running the Application

```bash
conda activate cubewise
streamlit run cw_water_budget_app.py
```

### Dependencies

- streamlit
- pandas
- numpy
- plotly
- openpyxl (for Excel file reading)

## Key Features Implementation

### Data Cleaning & Processing

- Automatic handling of datetime columns from Excel
- Data type conversion and null value handling
- Consistent column naming across divisions
- Integration of pricing data with volume data

### Advanced Analytics

- **Cumulative Calculations**: YTD and QTD volume tracking
- **Seasonality Analysis**: Monthly distribution patterns
- **Revenue Integration**: Price × Volume calculations by channel
- **Financial Modeling**: Complete expense and rebate calculations

### Interactive Interface

- **Sidebar Navigation**: Easy switching between challenges
- **Dynamic Filters**: All filters include "All" option for comprehensive analysis
- **Responsive Design**: Wide layout with proper spacing
- **Professional Styling**: Custom CSS for better presentation

## Business Logic

### Rebate Calculations

- **7-Eleven Rebate**: Specifically for 500mL bottles only
- **Safeway Rebate**: Applied to all cases regardless of SKU

### Revenue Calculations

- Channel-specific pricing applied to volume data
- Handles missing price mappings gracefully
- Calculates average revenue per case across all SKUs

### Expense Modeling

- **Fixed Expenses**: Marketing, Insurance, Administrative, etc.
- **Variable Expenses**: Utilities, Maintenance, R&D, etc.
- **Delivery Costs**: Based on fleet size and fuel efficiency

## Output & Reporting

### Challenge 1 Outputs

- Volume metrics and trends
- Multi-dimensional pivot tables
- Time-series analysis (MTD, QTD, YTD)

### Challenge 2 Outputs

- Revenue projections by month
- Seasonality percentages
- Average revenue per case

### Challenge 3 Outputs

- Complete projected income statement
- Detailed rebate calculations
- Comprehensive expense modeling

## Technical Architecture

- **Caching**: `@st.cache_data` for efficient data loading
- **Error Handling**: Graceful handling of data issues
- **Modular Design**: Separate functions for each challenge
- **Scalable Structure**: Easy to extend with additional features

## Troubleshooting

### Data Loading Issues

If you encounter "Can only use .dt accessor with datetimelike values" error:

1. **Clear Streamlit Cache**:

   ```bash
   python clear_cache.py
   ```

2. **Manual Cache Clear**:

   ```bash
   rm -rf ~/.streamlit .streamlit __pycache__
   streamlit run cw_water_budget_app.py
   ```

3. **Check Data File**: Ensure `Data.xls` is in the correct directory and not corrupted

### Common Issues

- **Port Already in Use**: Try different ports (8501, 8502, 8503, etc.)
- **Missing Dependencies**: Run `pip install -r requirements.txt`
- **Excel File Issues**: Ensure all four sheets exist: 'US - East', 'US - West', 'INTL', 'Prices'

## Deployment

### Local Development

```bash
streamlit run cw_water_budget_app.py
```

### Cloud Deployment (Streamlit Community Cloud)

1. Push code to GitHub repository
2. Visit [share.streamlit.io](https://share.streamlit.io)
3. Connect your GitHub account
4. Deploy from your repository

### Alternative Deployment Options

- **Heroku**: For custom domain and more control
- **AWS EC2**: For enterprise deployment
- **Google Cloud Run**: For scalable deployment
- **Azure Container Instances**: For Microsoft ecosystem

## Future Enhancements

- Export functionality for reports
- Additional visualization options
- Scenario planning capabilities
- Integration with external data sources
- Advanced forecasting models

---

**Note**: Ensure the `Data.xls` file is in the same directory as the application for proper data loading.
