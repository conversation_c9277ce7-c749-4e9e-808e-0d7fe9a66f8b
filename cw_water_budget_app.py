import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="CW Water Budget Planning 2025",
    page_icon="💧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .challenge-header {
        font-size: 1.8rem;
        font-weight: bold;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .filter-container {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)


@st.cache_data
def load_data():
    """Load and process all data from Excel file"""
    try:
        # Load all sheets
        us_east = pd.read_excel('Data.xls', sheet_name='US - East')
        us_west = pd.read_excel('Data.xls', sheet_name='US - West')
        intl = pd.read_excel('Data.xls', sheet_name='INTL')
        prices = pd.read_excel('Data.xls', sheet_name='Prices')

        # Clean column names for date columns
        date_columns = []
        for col in us_east.columns:
            if isinstance(col, datetime):
                date_columns.append(col.strftime('%Y-%m'))

        # Rename date columns to be consistent
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

        # Process US East data
        us_east_clean = us_east.copy()
        us_east_clean['Division'] = 'US-East'
        us_east_clean['Region'] = us_east_clean['State']

        # Process US West data
        us_west_clean = us_west.copy()
        us_west_clean['Division'] = 'US-West'
        us_west_clean['Region'] = us_west_clean['State']

        # Process International data
        intl_clean = intl.copy()
        intl_clean['Division'] = 'International'
        intl_clean['Region'] = intl_clean['Country']
        intl_clean['State'] = intl_clean['Country']  # For consistency

        # Combine all sales data
        all_data = []

        for df, division in [(us_east_clean, 'US-East'), (us_west_clean, 'US-West'), (intl_clean, 'International')]:
            # Get datetime columns
            datetime_cols = [
                col for col in df.columns if isinstance(col, datetime)]

            # Check if required columns exist
            required_cols = ['Customer', 'Channel',
                             'Region', 'SKU', 'Division']
            missing_cols = [
                col for col in required_cols if col not in df.columns]
            if missing_cols:
                st.error(f"Missing columns in {division}: {missing_cols}")
                continue

            df_melted = df.melt(
                id_vars=required_cols,
                value_vars=datetime_cols,
                var_name='Date',
                value_name='Volume'
            )

            # Ensure Date column is datetime type
            df_melted['Date'] = pd.to_datetime(df_melted['Date'])
            df_melted['Month'] = df_melted['Date'].dt.month
            df_melted['Month_Name'] = df_melted['Date'].dt.strftime('%b')
            df_melted['Year'] = df_melted['Date'].dt.year
            all_data.append(df_melted)

        combined_data = pd.concat(all_data, ignore_index=True)

        # Clean volume data
        combined_data['Volume'] = pd.to_numeric(
            combined_data['Volume'], errors='coerce').fillna(0)

        # Process prices data
        prices_clean = prices.copy()
        prices_clean = prices_clean.rename(columns={'Unnamed: 0': 'SKU'})
        prices_clean = prices_clean.set_index('SKU')

        return combined_data, prices_clean

    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        return None, None


def create_time_aggregations(df):
    """Create MTD, QTD, YTD aggregations"""
    df = df.copy()

    # Add quarter information
    df['Quarter'] = df['Month'].apply(lambda x: f"Q{((x-1)//3)+1}")

    # Create cumulative aggregations
    df_sorted = df.sort_values(
        ['Division', 'Customer', 'Channel', 'Region', 'SKU', 'Month'])

    # YTD calculation
    df_sorted['Volume_YTD'] = df_sorted.groupby(
        ['Division', 'Customer', 'Channel', 'Region', 'SKU'])['Volume'].cumsum()

    # QTD calculation
    df_sorted['Volume_QTD'] = df_sorted.groupby(
        ['Division', 'Customer', 'Channel', 'Region', 'SKU', 'Quarter'])['Volume'].cumsum()

    return df_sorted


def main():
    st.markdown('<div class="main-header">💧 CW Water Global Budget Planning 2025</div>',
                unsafe_allow_html=True)

    # Load data
    data, prices = load_data()

    if data is None or prices is None:
        st.error("Failed to load data. Please check the Data.xls file.")
        return

    # Create time aggregations
    data_with_agg = create_time_aggregations(data)

    # Sidebar for navigation
    st.sidebar.title("Navigation")
    challenge = st.sidebar.selectbox(
        "Select Challenge",
        ["Challenge 1: Multi-Dimensional Analysis",
         "Challenge 2: Income Statement",
         "Challenge 3: Expenses Model"]
    )

    if challenge == "Challenge 1: Multi-Dimensional Analysis":
        challenge_1(data_with_agg, prices)
    elif challenge == "Challenge 2: Income Statement":
        challenge_2(data_with_agg, prices)
    elif challenge == "Challenge 3: Expenses Model":
        challenge_3(data_with_agg, prices)


def challenge_1(data, prices):
    st.markdown('<div class="challenge-header">Challenge 1: Multi-Dimensional Analysis</div>',
                unsafe_allow_html=True)

    # Filters in sidebar
    st.sidebar.markdown("### Filters")

    # Division filter
    divisions = ['All'] + sorted(data['Division'].unique().tolist())
    selected_division = st.sidebar.selectbox("Division", divisions)

    # Customer filter
    customers = ['All'] + sorted(data['Customer'].unique().tolist())
    selected_customer = st.sidebar.selectbox("Customer", customers)

    # Channel filter
    channels = ['All'] + sorted(data['Channel'].unique().tolist())
    selected_channel = st.sidebar.selectbox("Channel", channels)

    # Region filter
    regions = ['All'] + sorted(data['Region'].unique().tolist())
    selected_region = st.sidebar.selectbox("Region", regions)

    # SKU filter
    skus = ['All'] + sorted(data['SKU'].unique().tolist())
    selected_sku = st.sidebar.selectbox("SKU", skus)

    # Apply filters
    filtered_data = data.copy()

    if selected_division != 'All':
        filtered_data = filtered_data[filtered_data['Division']
                                      == selected_division]
    if selected_customer != 'All':
        filtered_data = filtered_data[filtered_data['Customer']
                                      == selected_customer]
    if selected_channel != 'All':
        filtered_data = filtered_data[filtered_data['Channel']
                                      == selected_channel]
    if selected_region != 'All':
        filtered_data = filtered_data[filtered_data['Region']
                                      == selected_region]
    if selected_sku != 'All':
        filtered_data = filtered_data[filtered_data['SKU'] == selected_sku]

    # Display key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_volume = filtered_data['Volume'].sum()
        st.metric("Total Volume (Cases)", f"{total_volume:,.0f}")

    with col2:
        total_customers = filtered_data['Customer'].nunique()
        st.metric("Unique Customers", f"{total_customers:,}")

    with col3:
        total_skus = filtered_data['SKU'].nunique()
        st.metric("Active SKUs", f"{total_skus:,}")

    with col4:
        avg_monthly = filtered_data.groupby('Month')['Volume'].sum().mean()
        st.metric("Avg Monthly Volume", f"{avg_monthly:,.0f}")

    # Time period analysis tabs
    tab1, tab2, tab3, tab4 = st.tabs(
        ["Monthly View", "Quarterly View", "YTD View", "Data Table"])

    with tab1:
        st.subheader("Monthly Volume Analysis")
        monthly_data = filtered_data.groupby(['Month_Name', 'Month'])[
            'Volume'].sum().reset_index()
        monthly_data = monthly_data.sort_values('Month')

        fig = px.bar(monthly_data, x='Month_Name', y='Volume',
                     title="Monthly Volume Distribution",
                     labels={'Volume': 'Volume (Cases)', 'Month_Name': 'Month'})
        st.plotly_chart(fig, use_container_width=True)

        # MTD, QTD, YTD comparison
        col1, col2 = st.columns(2)
        with col1:
            st.subheader("Volume by Division")
            div_data = filtered_data.groupby(
                'Division')['Volume'].sum().reset_index()
            fig_div = px.pie(div_data, values='Volume', names='Division',
                             title="Volume Distribution by Division")
            st.plotly_chart(fig_div, use_container_width=True)

        with col2:
            st.subheader("Volume by Channel")
            channel_data = filtered_data.groupby(
                'Channel')['Volume'].sum().reset_index()
            fig_channel = px.pie(channel_data, values='Volume', names='Channel',
                                 title="Volume Distribution by Channel")
            st.plotly_chart(fig_channel, use_container_width=True)

    with tab2:
        st.subheader("Quarterly Analysis")
        quarterly_data = filtered_data.groupby(['Quarter', 'Division'])[
            'Volume'].sum().reset_index()

        fig_q = px.bar(quarterly_data, x='Quarter', y='Volume', color='Division',
                       title="Quarterly Volume by Division",
                       labels={'Volume': 'Volume (Cases)'})
        st.plotly_chart(fig_q, use_container_width=True)

        # QTD metrics
        qtd_data = filtered_data.groupby(
            ['Quarter'])['Volume_QTD'].max().reset_index()
        st.subheader("Quarter-to-Date (QTD) Volumes")
        st.dataframe(qtd_data, use_container_width=True)

    with tab3:
        st.subheader("Year-to-Date Analysis")
        ytd_data = filtered_data.groupby(['Month', 'Month_Name'])[
            'Volume_YTD'].max().reset_index()
        ytd_data = ytd_data.sort_values('Month')

        fig_ytd = px.line(ytd_data, x='Month_Name', y='Volume_YTD',
                          title="Year-to-Date Cumulative Volume",
                          labels={'Volume_YTD': 'Cumulative Volume (Cases)', 'Month_Name': 'Month'})
        st.plotly_chart(fig_ytd, use_container_width=True)

    with tab4:
        st.subheader("Detailed Data View")

        # Aggregation level selector
        agg_level = st.selectbox("Aggregation Level",
                                 ["Customer", "Channel", "Region", "SKU", "Division"])

        if agg_level:
            summary_data = filtered_data.groupby([agg_level, 'Month_Name']).agg({
                'Volume': 'sum',
                'Volume_YTD': 'max',
                'Volume_QTD': 'max'
            }).reset_index()

            # Pivot for better display
            pivot_data = summary_data.pivot(
                index=agg_level, columns='Month_Name', values='Volume')
            pivot_data = pivot_data.fillna(0)

            # Add total column
            pivot_data['Total'] = pivot_data.sum(axis=1)

            st.dataframe(pivot_data, use_container_width=True)


def challenge_2(data, prices):
    st.markdown('<div class="challenge-header">Challenge 2: Income Statement Template</div>',
                unsafe_allow_html=True)

    # Summary template with volume analysis
    st.subheader("Volume Summary by Region and Channel")

    # Monthly and annual volume by region and channel
    volume_summary = data.groupby(['Region', 'Channel', 'Month_Name']).agg({
        'Volume': 'sum'
    }).reset_index()

    # Create pivot table
    volume_pivot = volume_summary.pivot_table(
        index=['Region', 'Channel'],
        columns='Month_Name',
        values='Volume',
        fill_value=0
    )

    # Add annual total
    volume_pivot['Annual Total'] = volume_pivot.sum(axis=1)

    st.dataframe(volume_pivot, use_container_width=True)

    # Seasonality Analysis
    st.subheader("Seasonality Analysis")

    # Calculate overall seasonality
    monthly_totals = data.groupby('Month_Name')['Volume'].sum()
    annual_total = monthly_totals.sum()
    seasonality = (monthly_totals / annual_total * 100).round(2)

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Monthly Seasonality (%)")
        seasonality_df = seasonality.reset_index()
        seasonality_df.columns = ['Month', 'Percentage']

        fig_season = px.bar(seasonality_df, x='Month', y='Percentage',
                            title="Monthly Volume Seasonality",
                            labels={'Percentage': '% of Annual Volume'})
        st.plotly_chart(fig_season, use_container_width=True)

    with col2:
        st.subheader("Seasonality by SKU")
        sku_seasonality = data.groupby(['SKU', 'Month_Name'])[
            'Volume'].sum().reset_index()
        sku_pivot = sku_seasonality.pivot(
            index='SKU', columns='Month_Name', values='Volume')
        sku_pivot = sku_pivot.fillna(0)

        # Calculate percentage for each SKU
        sku_percentages = sku_pivot.div(sku_pivot.sum(axis=1), axis=0) * 100
        st.dataframe(sku_percentages.round(2), use_container_width=True)

    # Revenue Calculations
    st.subheader("Revenue Analysis")

    # Merge volume data with prices
    revenue_data = calculate_revenue(data, prices)

    # Monthly revenue
    monthly_revenue = revenue_data.groupby(
        'Month_Name')['Revenue'].sum().reset_index()
    monthly_revenue = monthly_revenue.sort_values('Month_Name')

    fig_revenue = px.bar(monthly_revenue, x='Month_Name', y='Revenue',
                         title="Monthly Revenue",
                         labels={'Revenue': 'Revenue ($)', 'Month_Name': 'Month'})
    st.plotly_chart(fig_revenue, use_container_width=True)

    # Average revenue per case
    total_volume = revenue_data['Volume'].sum()
    total_revenue = revenue_data['Revenue'].sum()
    avg_revenue_per_case = total_revenue / total_volume if total_volume > 0 else 0

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Annual Revenue", f"${total_revenue:,.2f}")
    with col2:
        st.metric("Total Annual Volume", f"{total_volume:,.0f} cases")
    with col3:
        st.metric("Average Revenue/Case", f"${avg_revenue_per_case:.2f}")


def calculate_revenue(data, prices):
    """Calculate revenue by merging volume data with pricing"""
    revenue_data = data.copy()

    # Create a mapping from SKU to channel prices
    price_mapping = {}
    for sku in prices.index:
        for channel in prices.columns:
            price_mapping[(sku, channel)] = prices.loc[sku, channel]

    # Map prices to data
    revenue_data['Price'] = revenue_data.apply(
        lambda row: price_mapping.get((row['SKU'], row['Channel']), 0), axis=1
    )

    # Calculate revenue
    revenue_data['Revenue'] = revenue_data['Volume'] * revenue_data['Price']

    return revenue_data


def challenge_3(data, prices):
    st.markdown('<div class="challenge-header">Challenge 3: Expenses Model</div>',
                unsafe_allow_html=True)

    # Calculate revenue first
    revenue_data = calculate_revenue(data, prices)

    st.subheader("Rebate Calculations")

    # 7-Eleven rebate: 10 cents/bottle for 500mL bottles (24 bottles per case)
    seven_eleven_data = revenue_data[
        (revenue_data['Customer'] == '7-Eleven') &
        (revenue_data['SKU'].str.contains('0.5L'))
    ].copy()

    seven_eleven_data['Rebate'] = seven_eleven_data['Volume'] * \
        24 * 0.10  # 24 bottles per case * $0.10
    seven_eleven_rebate = seven_eleven_data['Rebate'].sum()

    # Safeway rebate: 95 cents/case for all cases
    safeway_data = revenue_data[revenue_data['Customer'] == 'Safeway'].copy()
    safeway_data['Rebate'] = safeway_data['Volume'] * 0.95  # $0.95 per case
    safeway_rebate = safeway_data['Rebate'].sum()

    col1, col2 = st.columns(2)
    with col1:
        st.metric("7-Eleven Rebate (500mL bottles)",
                  f"${seven_eleven_rebate:,.2f}")
    with col2:
        st.metric("Safeway Rebate (All cases)", f"${safeway_rebate:,.2f}")

    # Delivery van costs
    st.subheader("Delivery Van Cost Model")

    col1, col2 = st.columns(2)
    with col1:
        st.info("Fleet Information")
        st.write("• Number of vans: 1,000")
        st.write("• Average miles per van per month: 10,234")
        st.write("• Total monthly miles: 10,234,000")

    with col2:
        st.subheader("Gasoline Cost Calculator")
        gas_price = st.number_input(
            "Cost per gallon ($)", min_value=0.0, value=3.50, step=0.01)
        mpg = st.number_input("Miles per gallon",
                              min_value=1.0, value=8.0, step=0.1)

        if gas_price > 0 and mpg > 0:
            monthly_gallons = 10234000 / mpg
            monthly_gas_cost = monthly_gallons * gas_price
            annual_gas_cost = monthly_gas_cost * 12

            st.metric("Monthly Gas Cost", f"${monthly_gas_cost:,.2f}")
            st.metric("Annual Gas Cost", f"${annual_gas_cost:,.2f}")

    # Other expense placeholders
    st.subheader("Other Business Expenses (Placeholders)")

    expense_categories = {
        "Marketing & Advertising": 0,
        "Sales Commissions": 0,
        "Warehouse & Distribution": 0,
        "Manufacturing Costs": 0,
        "Administrative Expenses": 0,
        "Insurance": 0,
        "Utilities": 0,
        "Equipment Maintenance": 0,
        "Professional Services": 0,
        "Research & Development": 0
    }

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Fixed Expenses (Annual)")
        for i, (category, default_value) in enumerate(list(expense_categories.items())[:5]):
            expense_categories[category] = st.number_input(
                f"{category} ($)",
                min_value=0.0,
                value=float(default_value),
                step=1000.0,
                key=f"fixed_{i}"
            )

    with col2:
        st.subheader("Variable Expenses (Annual)")
        for i, (category, default_value) in enumerate(list(expense_categories.items())[5:]):
            expense_categories[category] = st.number_input(
                f"{category} ($)",
                min_value=0.0,
                value=float(default_value),
                step=1000.0,
                key=f"variable_{i}"
            )

    # Summary Income Statement
    st.subheader("Projected Income Statement Summary")

    total_revenue = revenue_data['Revenue'].sum()
    total_rebates = seven_eleven_rebate + safeway_rebate
    total_other_expenses = sum(expense_categories.values())

    # Calculate gas cost if provided
    gas_cost_annual = 0
    if 'annual_gas_cost' in locals():
        gas_cost_annual = annual_gas_cost

    net_revenue = total_revenue - total_rebates
    total_expenses = total_other_expenses + gas_cost_annual
    projected_net_income = net_revenue - total_expenses

    # Display income statement
    income_statement = pd.DataFrame({
        'Item': [
            'Gross Revenue',
            'Less: Customer Rebates',
            'Net Revenue',
            'Less: Delivery Costs',
            'Less: Other Operating Expenses',
            'Projected Net Income'
        ],
        'Amount ($)': [
            f"{total_revenue:,.2f}",
            f"({total_rebates:,.2f})",
            f"{net_revenue:,.2f}",
            f"({gas_cost_annual:,.2f})",
            f"({total_other_expenses:,.2f})",
            f"{projected_net_income:,.2f}"
        ]
    })

    st.dataframe(income_statement, use_container_width=True, hide_index=True)

    # Key metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Gross Revenue", f"${total_revenue:,.2f}")
    with col2:
        st.metric("Total Expenses", f"${total_expenses + total_rebates:,.2f}")
    with col3:
        color = "normal" if projected_net_income >= 0 else "inverse"
        st.metric("Projected Net Income", f"${projected_net_income:,.2f}")


if __name__ == "__main__":
    main()
