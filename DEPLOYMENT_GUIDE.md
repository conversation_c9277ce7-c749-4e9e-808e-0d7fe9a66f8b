# 🚀 CW Water Budget App 部署指南

## 方案1：Streamlit Community Cloud（推荐 - 免费）

### 📋 准备工作
1. **GitHub账户**：确保您有GitHub账户
2. **数据文件**：确保`Data.xls`文件在项目根目录

### 🔧 部署步骤

#### 步骤1：推送代码到GitHub
```bash
# 在项目目录中执行
git add .
git commit -m "Initial commit: CW Water Budget Planning App"

# 创建GitHub仓库后，添加远程仓库
git remote add origin https://github.com/YOUR_USERNAME/cw-water-budget.git
git branch -M main
git push -u origin main
```

#### 步骤2：部署到Streamlit Cloud
1. 访问 [share.streamlit.io](https://share.streamlit.io)
2. 点击 "Sign in with GitHub"
3. 授权Streamlit访问您的GitHub
4. 点击 "New app"
5. 选择您的仓库：`YOUR_USERNAME/cw-water-budget`
6. 主文件路径：`cw_water_budget_app.py`
7. 点击 "Deploy!"

#### 步骤3：等待部署完成
- 部署通常需要2-5分钟
- 您将获得一个公共URL：`https://YOUR_APP_NAME.streamlit.app`

### ✅ 优点
- ✅ 完全免费
- ✅ 自动HTTPS
- ✅ 自动更新（推送到GitHub时）
- ✅ 无需服务器管理

### ⚠️ 限制
- 资源限制（1GB RAM）
- 公共访问（无法设置密码保护）
- Streamlit品牌显示

---

## 方案2：Heroku部署（付费）

### 📋 准备工作
```bash
# 安装Heroku CLI
# macOS: brew install heroku/brew/heroku
# Windows: 下载安装包

# 登录Heroku
heroku login
```

### 🔧 部署步骤

#### 步骤1：创建Heroku应用
```bash
heroku create cw-water-budget-app
```

#### 步骤2：创建Procfile
```bash
echo "web: streamlit run cw_water_budget_app.py --server.port=\$PORT --server.address=0.0.0.0" > Procfile
```

#### 步骤3：部署
```bash
git add .
git commit -m "Add Heroku deployment files"
git push heroku main
```

### ✅ 优点
- ✅ 自定义域名
- ✅ 更多资源
- ✅ 可以添加认证
- ✅ 专业外观

### ⚠️ 成本
- 基础版：$7/月
- 标准版：$25/月

---

## 方案3：AWS EC2部署（企业级）

### 📋 准备工作
1. AWS账户
2. EC2实例（推荐t3.small或更大）

### 🔧 部署步骤

#### 步骤1：启动EC2实例
```bash
# 连接到EC2实例
ssh -i your-key.pem ubuntu@your-ec2-ip

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip -y
```

#### 步骤2：部署应用
```bash
# 克隆代码
git clone https://github.com/YOUR_USERNAME/cw-water-budget.git
cd cw-water-budget

# 安装依赖
pip3 install -r requirements.txt

# 使用nohup后台运行
nohup streamlit run cw_water_budget_app.py --server.port=8501 --server.address=0.0.0.0 &
```

#### 步骤3：配置安全组
- 开放端口8501
- 配置HTTPS（可选）

### ✅ 优点
- ✅ 完全控制
- ✅ 可扩展
- ✅ 企业级安全
- ✅ 自定义配置

### ⚠️ 复杂度
- 需要服务器管理知识
- 需要配置安全和备份

---

## 🔒 安全考虑

### 数据保护
```python
# 在应用中添加密码保护（可选）
import streamlit as st

def check_password():
    def password_entered():
        if st.session_state["password"] == "your_secure_password":
            st.session_state["password_correct"] = True
            del st.session_state["password"]
        else:
            st.session_state["password_correct"] = False

    if "password_correct" not in st.session_state:
        st.text_input("Password", type="password", 
                     on_change=password_entered, key="password")
        return False
    elif not st.session_state["password_correct"]:
        st.text_input("Password", type="password", 
                     on_change=password_entered, key="password")
        st.error("Password incorrect")
        return False
    else:
        return True

# 在main()函数开始处添加
if not check_password():
    st.stop()
```

### 环境变量
```bash
# 创建.env文件（不要提交到Git）
ADMIN_PASSWORD=your_secure_password
DATABASE_URL=your_database_url
```

---

## 📊 监控和维护

### 应用监控
- 使用Streamlit Cloud的内置分析
- 设置错误通知
- 监控应用性能

### 数据更新
- 定期更新`Data.xls`文件
- 考虑连接到实时数据源
- 设置自动数据刷新

---

## 🎯 推荐部署流程

1. **开发阶段**：本地运行和测试
2. **演示阶段**：使用Streamlit Community Cloud
3. **生产阶段**：根据需求选择Heroku或AWS

## 📞 技术支持

如果在部署过程中遇到问题：
1. 检查日志文件
2. 验证所有依赖项
3. 确认数据文件路径
4. 检查网络和防火墙设置

---

**注意**：确保在部署前测试所有功能，并备份重要数据。
