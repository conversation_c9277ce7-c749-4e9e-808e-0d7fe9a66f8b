#!/bin/bash

# CW Water Budget App 部署脚本

echo "🚀 CW Water Budget App 部署助手"
echo "=================================="

# 检查Git状态
if [ ! -d ".git" ]; then
    echo "❌ 错误：当前目录不是Git仓库"
    echo "请先运行: git init"
    exit 1
fi

# 检查必要文件
if [ ! -f "cw_water_budget_app.py" ]; then
    echo "❌ 错误：找不到主应用文件 cw_water_budget_app.py"
    exit 1
fi

if [ ! -f "Data.xls" ]; then
    echo "❌ 错误：找不到数据文件 Data.xls"
    exit 1
fi

if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误：找不到依赖文件 requirements.txt"
    exit 1
fi

echo "✅ 所有必要文件检查通过"

# 提交更改
echo "📝 提交最新更改..."
git add .
git status

read -p "是否要提交这些更改？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    read -p "请输入提交信息: " commit_message
    git commit -m "$commit_message"
    echo "✅ 更改已提交"
else
    echo "⏭️  跳过提交"
fi

# 检查远程仓库
if ! git remote get-url origin > /dev/null 2>&1; then
    echo "⚠️  未设置远程仓库"
    echo "请按以下步骤操作："
    echo "1. 在GitHub上创建新仓库"
    echo "2. 运行: git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPO.git"
    echo "3. 运行: git push -u origin main"
else
    echo "📤 推送到远程仓库..."
    git push
    echo "✅ 代码已推送到GitHub"
fi

echo ""
echo "🌐 部署选项："
echo "1. Streamlit Community Cloud (免费)"
echo "   - 访问: https://share.streamlit.io"
echo "   - 使用GitHub账户登录"
echo "   - 选择您的仓库进行部署"
echo ""
echo "2. 本地测试"
echo "   - 运行: streamlit run cw_water_budget_app.py"
echo ""
echo "📖 详细部署指南请查看: DEPLOYMENT_GUIDE.md"
echo ""
echo "🎉 部署准备完成！"
