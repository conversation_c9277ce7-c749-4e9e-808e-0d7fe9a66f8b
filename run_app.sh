#!/bin/bash

# CW Water Budget Planning App Launcher
echo "🚀 Starting CW Water Budget Planning Application..."

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Conda not found. Please install <PERSON>conda or Miniconda first."
    exit 1
fi

# Check if cubewise environment exists
if ! conda env list | grep -q "cubewise"; then
    echo "❌ 'cubewise' conda environment not found."
    echo "Please create it first with: conda create -n cubewise python=3.9"
    exit 1
fi

# Check if Data.xls exists
if [ ! -f "Data.xls" ]; then
    echo "❌ Data.xls file not found in current directory."
    echo "Please ensure the Excel data file is present."
    exit 1
fi

# Activate environment and run app
echo "✅ Activating cubewise environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate cubewise

echo "✅ Installing/updating required packages..."
pip install -r requirements.txt

echo "✅ Launching Streamlit application..."
echo "📊 The app will open in your browser at http://localhost:8502"
echo "🛑 Press Ctrl+C to stop the application"

streamlit run cw_water_budget_app.py
