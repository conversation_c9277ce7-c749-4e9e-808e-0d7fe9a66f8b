#!/usr/bin/env python3
"""
Clear Streamlit cache and restart the CW Water Budget Planning app
"""

import os
import shutil
import subprocess
import sys

def clear_streamlit_cache():
    """Clear Streamlit cache directories"""
    cache_dirs = [
        os.path.expanduser("~/.streamlit"),
        ".streamlit",
        "__pycache__",
        ".cache"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                if os.path.isdir(cache_dir):
                    shutil.rmtree(cache_dir)
                    print(f"✅ Cleared cache directory: {cache_dir}")
                else:
                    os.remove(cache_dir)
                    print(f"✅ Cleared cache file: {cache_dir}")
            except Exception as e:
                print(f"⚠️  Could not clear {cache_dir}: {e}")

def main():
    print("🧹 Clearing Streamlit cache...")
    clear_streamlit_cache()
    
    print("\n🚀 Starting fresh CW Water Budget Planning app...")
    try:
        # Run the Streamlit app
        subprocess.run([
            "streamlit", "run", "cw_water_budget_app.py", 
            "--server.port", "8504",
            "--server.headless", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 App stopped by user")
    except Exception as e:
        print(f"❌ Error starting app: {e}")

if __name__ == "__main__":
    main()
